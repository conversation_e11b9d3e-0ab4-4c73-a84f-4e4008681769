from qiskit import QuantumCircuit
from qiskit.quantum_info import Statevector
import matplotlib.pyplot as plt
import numpy as np

def galton_box_circuit(num_layers):
    """
    Create a Galton Box (quantum walk) circuit with the given number of layers.
    Each layer is represented by a qubit with a Hadamard gate.
    """
    qc = QuantumCircuit(num_layers)
    for i in range(num_layers):
        qc.h(i)
    return qc

def run_galton_box(num_layers, shots=1024):
    qc = galton_box_circuit(num_layers)
    state = Statevector.from_instruction(qc)
    probs_dict = state.probabilities_dict()
    bitstrings = list(probs_dict.keys())
    probs = list(probs_dict.values())
    samples = np.random.choice(bitstrings, size=shots, p=probs)
    counts = {}
    for bstr in samples:
        counts[bstr] = counts.get(bstr, 0) + 1
    return counts

def plot_distribution(counts, num_layers):
    # Convert bitstrings to number of '1's (number of right moves)
    distribution = {}
    for bitstring, count in counts.items():
        num_right = bitstring.count('1')
        distribution[num_right] = distribution.get(num_right, 0) + count
    # Sort by number of right moves
    sorted_dist = dict(sorted(distribution.items()))
    plt.bar(sorted_dist.keys(), sorted_dist.values())
    plt.xlabel('Number of right moves (number of 1s)')
    plt.ylabel('Counts')
    plt.title(f'Galton Box Distribution ({num_layers} layers)')
    plt.xticks(np.arange(0, num_layers+1, 1))
    plt.show()

if __name__ == "__main__":
    num_layers = 8  
    counts = run_galton_box(num_layers)
    plot_distribution(counts, num_layers)
