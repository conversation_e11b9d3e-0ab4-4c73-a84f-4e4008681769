from qiskit import QuantumCircuit
from qiskit_aer import AerSimulator
import matplotlib.pyplot as plt
import numpy as np

def galton_box_uss_circuit(num_layers, row_angles=None):
    """
    Create a Galton Box circuit using Universal Statistical Simulator (USS)
    as described by <PERSON><PERSON> & Varcoe, with phase-programmable pegs.
    
    Args:
        num_layers: Number of layers in the Galton Box
        row_angles: List of rotation angles (in radians) for each row.
                   If None, uses unbiased pegs (all Hadamard gates)
    
    Returns:
        QuantumCircuit: The USS Galton Box circuit with biased quantum pegs
    """
    # Correct USS specification:
    # - 1 control qubit (qubit 0)
    # - 2n+1 data qubits (qubits 1 to 2n+1)
    # - 2n+1 classical bits for measurement
    data_qubits = 2 * num_layers + 1
    total_qubits = 1 + data_qubits  # 1 control + data qubits
    total_clbits = data_qubits
    
    qc = QuantumCircuit(total_qubits, total_clbits)
    
    # Initialize: load "ball" in the center data qubit
    center = num_layers + 1  # Center qubit (index n+1)
    qc.x(center)
    
    # Default to unbiased pegs if no angles provided
    if row_angles is None:
        row_angles = [0.0] * num_layers  # 0 means use Hadamard (unbiased)
    
    # Apply USS layers with biased quantum pegs
    for r in range(num_layers):
        # Reset control qubit (only needed for first layer)
        if r == 0:
            qc.reset(0)

        # Apply biased rotation instead of Hadamard
        # Carney & Varcoe: replace H with RX(θ) for biased pegs
        if np.isclose(row_angles[r], 0.0):
            qc.h(0)  # Unbiased peg (Hadamard)
        else:
            qc.rx(row_angles[r], 0)  # Biased peg (RX rotation)

        # Define the rail for this row (length 2r+1)
        left = center - (r + 1)
        right = center + r

        # Walk across the full rail with CSWAP→CX→CSWAP pattern
        # The ball walks through every edge of the rail
        for k in range(left, right):
            qc.cswap(0, k, k + 1)
            qc.cx(k + 1, 0)  # Hand off control

        # End-of-row CSWAP to reach the far edge
        qc.cswap(0, right, right + 1)

        # Reset control qubit for next layer
        qc.reset(0)
    
    # Measure all data qubits (1 to 2n+1)
    # The one-hot '1' marks the bin directly
    measure_qubits = list(range(1, 1 + data_qubits))
    measure_clbits = list(range(data_qubits))
    qc.measure(measure_qubits, measure_clbits)
    
    return qc

def run_galton_box_uss(num_layers, shots=1024, row_angles=None):
    """
    Run Galton Box using Universal Statistical Simulator (USS) with biased quantum pegs
    """
    qc = galton_box_uss_circuit(num_layers, row_angles)
    
    # Create Aer simulator with statevector method for ideal simulation
    backend = AerSimulator(method="statevector")
    
    # Run the circuit with seed for reproducibility
    job = backend.run(qc, shots=shots, seed_simulator=42)
    result = job.result()
    counts = result.get_counts()
    
    return counts

def decode_uss_position(bitstring, num_layers):
    """
    Decode USS one-hot encoding to get the ball position.
    USS encoding: single '1' in data qubits indicates position.
    Map to bins 0 to n for comparison with binomial distribution.
    """
    # Little-endian bitstring decoding
    # result.get_counts() returns least-significant classical bit on the right
    data_qubit_index = bitstring[::-1].index('1')
    
    # Loss-free bin mapping: k = |j - n|
    # where j is the data qubit index and n is the number of layers
    bin_position = abs(data_qubit_index - num_layers)
    
    return bin_position

def decode_distribution(counts, num_layers):
    """Decode USS one-hot encoding to position distribution."""
    distribution = {}
    for bitstring, count in counts.items():
        position = decode_uss_position(bitstring, num_layers)
        distribution[position] = distribution.get(position, 0) + count
    return dict(sorted(distribution.items()))

def plot_distribution(counts, num_layers, title_suffix=""):
    """Plot a single distribution."""
    distribution = decode_distribution(counts, num_layers)

    plt.figure(figsize=(10, 6))
    plt.bar(distribution.keys(), distribution.values(), alpha=0.7, color='skyblue')
    plt.xlabel('Ball Position (decoded from USS)')
    plt.ylabel('Counts')
    plt.title(f'Galton Box USS Distribution ({num_layers} layers){title_suffix}')
    plt.xticks(range(len(distribution)))
    plt.grid(True, alpha=0.3)
    plt.show()

def compare_distributions(num_layers, unbiased_counts, biased_counts, row_angles):
    """Compare unbiased vs biased quantum peg distributions."""
    from scipy.stats import binom

    # Decode both distributions using helper function
    unbiased_dist = decode_distribution(unbiased_counts, num_layers)
    biased_dist = decode_distribution(biased_counts, num_layers)
    
    total_shots = sum(unbiased_dist.values())
    
    # Theoretical binomial distribution
    n = num_layers
    p = 0.5
    theoretical_probs = [binom.pmf(k, n, p) for k in range(n + 1)]
    theoretical_counts = [prob * total_shots for prob in theoretical_probs]
    
    # Plot comparison
    positions = list(range(n + 1))
    unbiased_counts_list = [unbiased_dist.get(pos, 0) for pos in positions]
    biased_counts_list = [biased_dist.get(pos, 0) for pos in positions]
    
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.bar(positions, unbiased_counts_list, alpha=0.7, label='Unbiased USS', color='skyblue')
    plt.plot(positions, theoretical_counts, 'ro-', label='Theoretical Binomial', linewidth=2)
    plt.xlabel('Ball Position')
    plt.ylabel('Counts')
    plt.title('Unbiased USS vs Theoretical')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.bar(positions, biased_counts_list, alpha=0.7, label='Biased USS', color='orange')
    plt.plot(positions, theoretical_counts, 'ro-', label='Theoretical Binomial', linewidth=2)
    plt.xlabel('Ball Position')
    plt.ylabel('Counts')
    plt.title(f'Biased USS (RX angles: {row_angles[:3]}...)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    # Plot difference between biased and unbiased
    difference = [biased - unbiased for biased, unbiased in zip(biased_counts_list, unbiased_counts_list)]
    plt.bar(positions, difference, alpha=0.7, color='green')
    plt.xlabel('Ball Position')
    plt.ylabel('Count Difference')
    plt.title('Bias Effect: Biased - Unbiased')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def calculate_distribution_stats(distribution):
    """Calculate mean and variance for a distribution."""
    total_count = sum(distribution.values())
    mean = sum(pos * count for pos, count in distribution.items()) / total_count
    variance = sum((pos - mean)**2 * count for pos, count in distribution.items()) / total_count
    return mean, variance

def print_distribution_analysis(distributions, num_layers):
    """Print comprehensive statistics for all distributions."""
    decoded_distributions = {name: decode_distribution(counts, num_layers)
                           for name, counts in distributions.items()}

    for case_name, distribution in decoded_distributions.items():
        mean, variance = calculate_distribution_stats(distribution)

        print(f"\n--- {case_name} ---")
        print(f"Mean: {mean:.2f} (theoretical unbiased: {num_layers/2:.2f})")
        print(f"Variance: {variance:.2f} (theoretical unbiased: {num_layers/4:.2f})")
        print(f"Distribution: {distribution}")

        # Calculate difference from unbiased
        if case_name != "Unbiased":
            unbiased_dist = decoded_distributions["Unbiased"]
            max_diff = max(abs(distribution.get(pos, 0) - unbiased_dist.get(pos, 0))
                          for pos in range(num_layers + 1))
            print(f"Max difference from unbiased: {max_diff}")

            # Show which positions are most affected
            differences = [(pos, distribution.get(pos, 0) - unbiased_dist.get(pos, 0))
                          for pos in range(num_layers + 1)]
            differences.sort(key=lambda x: abs(x[1]), reverse=True)
            print("Most affected positions:")
            for pos, diff in differences[:3]:
                if abs(diff) > 0:
                    print(f"  Position {pos}: {diff:+d}")

def run_bias_experiments(num_layers, shots):
    """Run all bias experiments and return results."""
    # Test cases with different bias patterns
    test_cases = {
        "Unbiased": None,
        "Rx bias (75:25)": [2*np.pi/3] * num_layers,
        "Phase-shifted 50:50 (π/2)": [np.pi/2] * num_layers,
        "Weak bias (87:13)": [np.pi/6] * num_layers,
        "Mixed bias": [np.pi/3, np.pi/2, np.pi/4, np.pi/6][:num_layers]
    }

    results = {}
    for name, angles in test_cases.items():
        results[name] = run_galton_box_uss(num_layers, shots, row_angles=angles)

    return results

def plot_comprehensive_analysis(distributions, num_layers):
    """Create comprehensive comparison plots."""
    decoded_distributions = {name: decode_distribution(counts, num_layers)
                           for name, counts in distributions.items()}

    plt.figure(figsize=(15, 10))
    positions = list(range(num_layers + 1))
    colors = ['skyblue', 'orange', 'green', 'red', 'purple']

    # Plot all distributions together
    plt.subplot(2, 2, 1)
    for i, (name, dist) in enumerate(decoded_distributions.items()):
        counts_list = [dist.get(pos, 0) for pos in positions]
        plt.bar([p + i*0.15 for p in positions], counts_list, width=0.15,
                alpha=0.7, label=name, color=colors[i])
    plt.xlabel('Ball Position')
    plt.ylabel('Counts')
    plt.title('All Biased Quantum Peg Distributions')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot differences from unbiased
    plt.subplot(2, 2, 2)
    unbiased_dist = decoded_distributions["Unbiased"]
    for i, (name, dist) in enumerate(decoded_distributions.items()):
        if name != "Unbiased":
            differences = [dist.get(pos, 0) - unbiased_dist.get(pos, 0) for pos in positions]
            plt.bar([p + i*0.2 for p in positions], differences, width=0.2,
                    alpha=0.7, label=name, color=colors[i])
    plt.xlabel('Ball Position')
    plt.ylabel('Count Difference from Unbiased')
    plt.title('Bias Effects: Difference from Unbiased')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot relative probabilities
    plt.subplot(2, 2, 3)
    total_shots = sum(unbiased_dist.values())
    for i, (name, dist) in enumerate(decoded_distributions.items()):
        probs = [dist.get(pos, 0) / total_shots for pos in positions]
        plt.plot(positions, probs, 'o-', label=name, color=colors[i], linewidth=2, markersize=6)
    plt.xlabel('Ball Position')
    plt.ylabel('Probability')
    plt.title('Relative Probabilities')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot bias angles
    plt.subplot(2, 2, 4)
    bias_angles = {
        "Rx bias (75:25)": [2*np.pi/3] * num_layers,
        "Strong bias (50:50)": [np.pi/2] * num_layers,
        "Weak bias (87:13)": [np.pi/6] * num_layers,
        "Mixed bias": [np.pi/3, np.pi/2, np.pi/4, np.pi/6][:num_layers]
    }

    for i, (name, angles) in enumerate(bias_angles.items()):
        plt.plot(range(len(angles)), angles, 'o-', label=name, color=colors[i+1],
                linewidth=2, markersize=6)
    plt.xlabel('Row Index')
    plt.ylabel('RX Angle (radians)')
    plt.title('Bias Angles Applied')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    num_layers = 4
    shots = 1024

    print(f"Running Galton Box USS with {num_layers} layers and {shots} shots...")
    print(f"Data qubits: {2*num_layers+1}, Total qubits: {2*num_layers+2}")

    # Run all bias experiments
    distributions = run_bias_experiments(num_layers, shots)

    # Plot individual distributions
    for name, counts in distributions.items():
        plot_distribution(counts, num_layers, f" ({name})")

    # Create comprehensive comparison plots
    plot_comprehensive_analysis(distributions, num_layers)

    # Print comprehensive statistics
    print(f"\n=== Comprehensive Biased Quantum Peg Analysis ===")
    print_distribution_analysis(distributions, num_layers)
