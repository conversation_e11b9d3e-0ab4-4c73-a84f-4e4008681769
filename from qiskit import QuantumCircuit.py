from qiskit import QuantumCircuit
from qiskit_aer import AerSimulator
import matplotlib.pyplot as plt
import numpy as np

def galton_box_uss_circuit(num_layers, row_angles=None):
    """
    Create a Galton Box circuit using Universal Statistical Simulator (USS)
    as described by <PERSON><PERSON> & Varcoe, with biased quantum pegs.
    
    Args:
        num_layers: Number of layers in the Galton Box
        row_angles: List of rotation angles (in radians) for each row.
                   If None, uses unbiased pegs (all Hadamard gates)
    
    Returns:
        QuantumCircuit: The USS Galton Box circuit with biased quantum pegs
    """
    # Correct USS specification:
    # - 1 control qubit (qubit 0)
    # - 2n+1 data qubits (qubits 1 to 2n+1)
    # - 2n+1 classical bits for measurement
    data_qubits = 2 * num_layers + 1
    total_qubits = 1 + data_qubits  # 1 control + data qubits
    total_clbits = data_qubits
    
    qc = QuantumCircuit(total_qubits, total_clbits)
    
    # Center qubit is at index n (not n+1)
    center = num_layers
    qc.x(center)
    
    # Default to unbiased pegs if no angles provided
    if row_angles is None:
        row_angles = [0.0] * num_layers  # 0 means use Hadamard (unbiased)
    
    # Apply USS layers with biased quantum pegs
    for r in range(num_layers):
        # Apply biased rotation instead of Hadamard
        if np.isclose(row_angles[r], 0.0):
            qc.h(0)  # Unbiased peg (Hadamard)
        else:
            qc.rx(row_angles[r], 0)  # Biased peg (RX rotation)

        # FULL span for row r (data-qubit indices, NOT classical bits)
        left = center - (r + 1)      # inclusive
        right = center + (r + 1)     # inclusive

        # Only run the peg loop if at least one triplet fits
        if right - left >= 2:
            for k in range(left, right - 1, 2):  # k+2 <= right
                qc.cswap(0, k, k + 1)
                qc.cx(k + 1, 0)
                qc.swap(k + 1, k + 2)

        # Reset control qubit for next layer
        qc.reset(0)
    
    # Measure all data qubits (0 to 2n)
    measure_qubits = list(range(data_qubits))
    measure_clbits = list(range(data_qubits))
    qc.measure(measure_qubits, measure_clbits)
    
    return qc

def run_galton_box_uss(num_layers, shots=1024, row_angles=None):
    """
    Run Galton Box using Universal Statistical Simulator (USS) with biased quantum pegs
    """
    qc = galton_box_uss_circuit(num_layers, row_angles)
    
    # Create Aer simulator with statevector method for ideal simulation
    backend = AerSimulator(method="statevector")
    
    # Run the circuit with seed for reproducibility
    job = backend.run(qc, shots=shots, seed_simulator=42)
    result = job.result()
    counts = result.get_counts()
    
    return counts

def decode_uss_position(bitstring, num_layers):
    """
    Paper-faithful USS decoder: bin = abs(idx - centre)
    """
    idx = bitstring[::-1].index('1')
    centre = num_layers
    return abs(idx - centre)

def decode_distribution(counts, num_layers):
    """Decode USS one-hot encoding to position distribution."""
    distribution = {}
    for bitstring, count in counts.items():
        position = decode_uss_position(bitstring, num_layers)
        distribution[position] = distribution.get(position, 0) + count
    return dict(sorted(distribution.items()))

def plot_distribution(counts, num_layers, title_suffix=""):
    """Plot a single distribution."""
    distribution = decode_distribution(counts, num_layers)

    plt.figure(figsize=(10, 6))
    plt.bar(distribution.keys(), distribution.values(), alpha=0.7, color='skyblue')
    plt.xlabel('Ball Position (decoded from USS)')
    plt.ylabel('Counts')
    plt.title(f'Galton Box USS Distribution ({num_layers} layers){title_suffix}')
    plt.xticks(range(len(distribution)))
    plt.grid(True, alpha=0.3)
    plt.show()

def compare_distributions(num_layers, unbiased_counts, biased_counts, row_angles):
    """Compare unbiased vs biased quantum peg distributions."""
    from scipy.stats import binom

    # Decode both distributions using helper function
    unbiased_dist = decode_distribution(unbiased_counts, num_layers)
    biased_dist = decode_distribution(biased_counts, num_layers)
    
    total_shots = sum(unbiased_dist.values())
    
    # Theoretical binomial distribution
    n = num_layers
    p = 0.5
    theoretical_probs = [binom.pmf(k, n, p) for k in range(n + 1)]
    theoretical_counts = [prob * total_shots for prob in theoretical_probs]
    
    # Plot comparison
    positions = list(range(n + 1))
    unbiased_counts_list = [unbiased_dist.get(pos, 0) for pos in positions]
    biased_counts_list = [biased_dist.get(pos, 0) for pos in positions]
    
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.bar(positions, unbiased_counts_list, alpha=0.7, label='Unbiased USS', color='skyblue')
    plt.plot(positions, theoretical_counts, 'ro-', label='Theoretical Binomial', linewidth=2)
    plt.xlabel('Ball Position')
    plt.ylabel('Counts')
    plt.title('Unbiased USS vs Theoretical')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.bar(positions, biased_counts_list, alpha=0.7, label='Biased USS', color='orange')
    plt.plot(positions, theoretical_counts, 'ro-', label='Theoretical Binomial', linewidth=2)
    plt.xlabel('Ball Position')
    plt.ylabel('Counts')
    plt.title(f'Biased USS (RX angles: {row_angles[:3]}...)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 3)
    # Plot difference between biased and unbiased
    difference = [biased - unbiased for biased, unbiased in zip(biased_counts_list, unbiased_counts_list)]
    plt.bar(positions, difference, alpha=0.7, color='green')
    plt.xlabel('Ball Position')
    plt.ylabel('Count Difference')
    plt.title('Bias Effect: Biased - Unbiased')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def calculate_distribution_stats(distribution):
    """Calculate mean and variance for a distribution."""
    total_count = sum(distribution.values())
    mean = sum(pos * count for pos, count in distribution.items()) / total_count
    variance = sum((pos - mean)**2 * count for pos, count in distribution.items()) / total_count
    return mean, variance

def print_distribution_analysis(distributions, num_layers):
    """Print comprehensive statistics for all distributions."""
    decoded_distributions = {name: decode_distribution(counts, num_layers)
                           for name, counts in distributions.items()}

    for case_name, distribution in decoded_distributions.items():
        mean, variance = calculate_distribution_stats(distribution)

        print(f"\n--- {case_name} ---")
        print(f"Mean: {mean:.2f} (theoretical unbiased: {num_layers/2:.2f})")
        print(f"Variance: {variance:.2f} (theoretical unbiased: {num_layers/4:.2f})")
        print(f"Distribution: {distribution}")

        # Calculate difference from unbiased
        if case_name != "Unbiased":
            unbiased_dist = decoded_distributions["Unbiased"]
            max_diff = max(abs(distribution.get(pos, 0) - unbiased_dist.get(pos, 0))
                          for pos in range(num_layers + 1))
            print(f"Max difference from unbiased: {max_diff}")

            # Show which positions are most affected
            differences = [(pos, distribution.get(pos, 0) - unbiased_dist.get(pos, 0))
                          for pos in range(num_layers + 1)]
            differences.sort(key=lambda x: abs(x[1]), reverse=True)
            print("Most affected positions:")
            for pos, diff in differences[:3]:
                if abs(diff) > 0:
                    print(f"  Position {pos}: {diff:+d}")

def run_bias_experiments(num_layers, shots):
    """Run all bias experiments and return results."""
    # Create mixed bias pattern that adapts to num_layers
    mixed_pattern = [np.pi/3, np.pi/2, np.pi/4, np.pi/6]
    mixed_bias = (mixed_pattern * ((num_layers // len(mixed_pattern)) + 1))[:num_layers]

    # Test cases with different bias patterns
    test_cases = {
        "Unbiased": None,
        "Rx bias (75:25)": [2*np.pi/3] * num_layers,
        "Phase-shifted 50:50 (π/2)": [np.pi/2] * num_layers,
        "Weak bias (87:13)": [np.pi/6] * num_layers,
        "Mixed bias": mixed_bias
    }

    results = {}
    for name, angles in test_cases.items():
        results[name] = run_galton_box_uss(num_layers, shots, row_angles=angles)

    return results

def plot_comprehensive_analysis(distributions, num_layers):
    """Create comprehensive comparison plots."""
    decoded_distributions = {name: decode_distribution(counts, num_layers)
                           for name, counts in distributions.items()}

    plt.figure(figsize=(15, 10))
    positions = list(range(num_layers + 1))
    colors = ['skyblue', 'orange', 'green', 'red', 'purple']

    # Plot all distributions together
    plt.subplot(2, 2, 1)
    for i, (name, dist) in enumerate(decoded_distributions.items()):
        counts_list = [dist.get(pos, 0) for pos in positions]
        plt.bar([p + i*0.15 for p in positions], counts_list, width=0.15,
                alpha=0.7, label=name, color=colors[i])
    plt.xlabel('Ball Position')
    plt.ylabel('Counts')
    plt.title('All Biased Quantum Peg Distributions')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot differences from unbiased
    plt.subplot(2, 2, 2)
    unbiased_dist = decoded_distributions["Unbiased"]
    for i, (name, dist) in enumerate(decoded_distributions.items()):
        if name != "Unbiased":
            differences = [dist.get(pos, 0) - unbiased_dist.get(pos, 0) for pos in positions]
            plt.bar([p + i*0.2 for p in positions], differences, width=0.2,
                    alpha=0.7, label=name, color=colors[i])
    plt.xlabel('Ball Position')
    plt.ylabel('Count Difference from Unbiased')
    plt.title('Bias Effects: Difference from Unbiased')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot relative probabilities
    plt.subplot(2, 2, 3)
    total_shots = sum(unbiased_dist.values())
    for i, (name, dist) in enumerate(decoded_distributions.items()):
        probs = [dist.get(pos, 0) / total_shots for pos in positions]
        plt.plot(positions, probs, 'o-', label=name, color=colors[i], linewidth=2, markersize=6)
    plt.xlabel('Ball Position')
    plt.ylabel('Probability')
    plt.title('Relative Probabilities')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot bias angles
    plt.subplot(2, 2, 4)
    # Create adaptive mixed bias pattern
    mixed_pattern = [np.pi/3, np.pi/2, np.pi/4, np.pi/6]
    mixed_bias = (mixed_pattern * ((num_layers // len(mixed_pattern)) + 1))[:num_layers]

    bias_angles = {
        "Rx bias (75:25)": [2*np.pi/3] * num_layers,
        "Phase-shifted 50:50 (π/2)": [np.pi/2] * num_layers,
        "Weak bias (87:13)": [np.pi/6] * num_layers,
        "Mixed bias": mixed_bias
    }

    for i, (name, angles) in enumerate(bias_angles.items()):
        plt.plot(range(len(angles)), angles, 'o-', label=name, color=colors[i+1],
                linewidth=2, markersize=6)
    plt.xlabel('Row Index')
    plt.ylabel('RX Angle (radians)')
    plt.title('Bias Angles Applied')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    num_layers = 6  # Test with 6 layers to see the full binomial distribution
    shots = 1024

    print(f"Running Galton Box USS with {num_layers} layers and {shots} shots...")
    print(f"Data qubits: {2*num_layers+1}, Total qubits: {2*num_layers+2}")

    # Run all bias experiments
    distributions = run_bias_experiments(num_layers, shots)

    # Debug the unbiased case to check raw measurements
    print("\n=== Raw Unbiased Measurements Debug ===")
    unbiased_counts = distributions["Unbiased"]
    print(f"Total measurements: {sum(unbiased_counts.values())}")

    # Check which qubits are being measured
    raw_idx = {i: 0 for i in range(2*num_layers+1)}
    for bitstring, count in unbiased_counts.items():
        if '1' in bitstring:
            qubit_idx = bitstring[::-1].index('1')
            raw_idx[qubit_idx] += count

    print("Raw qubit counts:", raw_idx)
    
    # Check if the distribution is symmetric around the center
    center_idx = num_layers
    print(f"Center qubit index: {center_idx}")
    
    # Check symmetry
    left_counts = sum(raw_idx[i] for i in range(center_idx))
    right_counts = sum(raw_idx[i] for i in range(center_idx + 1, 2*num_layers + 1))
    center_count = raw_idx[center_idx]
    
    print(f"Left of center (0-{center_idx-1}): {left_counts}")
    print(f"Center ({center_idx}): {center_count}")
    print(f"Right of center ({center_idx+1}-{2*num_layers}): {right_counts}")
    print(f"Symmetry check - Left vs Right: {left_counts} vs {right_counts}")
    
    # Expected binomial distribution for n=6: (1, 6, 15, 20, 15, 6, 1) * shots/64
    expected_binomial = [1, 6, 15, 20, 15, 6, 1]
    total_shots = sum(raw_idx.values())
    expected_counts = [count * total_shots / 64 for count in expected_binomial]
    print(f"Expected binomial counts: {expected_counts}")
    
    # Check which qubits correspond to which bins
    print("Qubit to bin mapping:")
    for idx in range(2*num_layers + 1):
        bin_pos = abs(idx - center_idx)
        print(f"  Qubit {idx} -> Bin {bin_pos}: {raw_idx[idx]} counts")

    # Plot individual distributions
    for name, counts in distributions.items():
        plot_distribution(counts, num_layers, f" ({name})")

    # Create comprehensive comparison plots
    plot_comprehensive_analysis(distributions, num_layers)

    # Print comprehensive statistics
    print(f"\n=== Comprehensive Biased Quantum Peg Analysis ===")
    print_distribution_analysis(distributions, num_layers)
    
    # One-line sanity check after both fixes
    print(f"\n=== One-line Sanity Check ===")
    unbiased = decode_distribution(run_galton_box_uss(6, 16384), 6)
    print("Unbiased distribution (16384 shots):", unbiased)
    print("Expected binomial (1:6:15:20:15:6:1):", [1, 6, 15, 20, 15, 6, 1])
    
    # Check if the distribution is symmetric
    if len(unbiased) >= 7:
        left_sum = sum(unbiased.get(i, 0) for i in range(4))
        right_sum = sum(unbiased.get(i, 0) for i in range(4, 7))
        print(f"Left vs Right symmetry: {left_sum} vs {right_sum}")
        print(f"Symmetry ratio: {left_sum/right_sum:.3f} (should be close to 1.0)")
