{".class": "MypyFile", "_fullname": "io", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BlockingIOError": {".class": "SymbolTableNode", "cross_ref": "_io.BlockingIOError", "kind": "Gdef"}, "BufferedIOBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_io._BufferedIOBase", "io.IOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "io.BufferedIOBase", "name": "BufferedIOBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "io.BufferedIOBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "io", "mro": ["io.BufferedIOBase", "_io._BufferedIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BufferedRWPair": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedRWPair", "kind": "Gdef"}, "BufferedRandom": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedRandom", "kind": "Gdef"}, "BufferedReader": {".class": "SymbolTableNode", "cross_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "BufferedWriter": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedWriter", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "DEFAULT_BUFFER_SIZE": {".class": "SymbolTableNode", "cross_ref": "_io.DEFAULT_BUFFER_SIZE", "kind": "Gdef"}, "FileIO": {".class": "SymbolTableNode", "cross_ref": "_io.FileIO", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IOBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_io._IOBase"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "io.IOBase", "name": "IOBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "io.IOBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "io", "mro": ["io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncrementalNewlineDecoder": {".class": "SymbolTableNode", "cross_ref": "_io.IncrementalNewlineDecoder", "kind": "Gdef"}, "RawIOBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_io._RawIOBase", "io.IOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "io.RawIOBase", "name": "RawIOBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "io.RawIOBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "io", "mro": ["io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEEK_CUR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "io.SEEK_CUR", "name": "SEEK_CUR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "SEEK_END": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "io.SEEK_END", "name": "SEEK_END", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "SEEK_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "io.SEEK_SET", "name": "SEEK_SET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "TextIOBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_io._TextIOBase", "io.IOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "io.TextIOBase", "name": "TextIOBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "io.TextIOBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "io", "mro": ["io.TextIOBase", "_io._TextIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "_io.TextIOWrapper", "kind": "Gdef"}, "UnsupportedOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "io.UnsupportedOperation", "name": "UnsupportedOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "io.UnsupportedOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "io", "mro": ["io.UnsupportedOperation", "builtins.OSError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BufferedIOBase": {".class": "SymbolTableNode", "cross_ref": "_io._BufferedIOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_IOBase": {".class": "SymbolTableNode", "cross_ref": "_io._IOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_RawIOBase": {".class": "SymbolTableNode", "cross_ref": "_io._RawIOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TextIOBase": {".class": "SymbolTableNode", "cross_ref": "_io._TextIOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_WrappedBuffer": {".class": "SymbolTableNode", "cross_ref": "_io._W<PERSON><PERSON><PERSON>er", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "io.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "io.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "io.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "io.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "io.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "io.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "io.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "open": {".class": "SymbolTableNode", "cross_ref": "_io.open", "kind": "Gdef"}, "open_code": {".class": "SymbolTableNode", "cross_ref": "_io.open_code", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "text_encoding": {".class": "SymbolTableNode", "cross_ref": "_io.text_encoding", "kind": "Gdef"}}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/io.pyi"}